package com.example.logfloat_plugin.demo

import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.logfloat_plugin.R
import com.example.logfloat_plugin.floating.FloatingWindow
import com.example.logfloat_plugin.floating.FloatingWindowManager
import com.example.logfloat_plugin.floating.FloatingWindowPermissions

class DemoActivity : AppCompatActivity() {
    
    private var floatingWindow: FloatingWindow? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_demo)
        
        // Check permissions
        if (!FloatingWindowPermissions.hasOverlayPermission(this)) {
            FloatingWindowPermissions.requestOverlayPermission(this)
            Toast.makeText(this, "请授予悬浮窗权限", Toast.LENGTH_LONG).show()
            return
        }
        
        setupUI()
    }
    
    private fun setupUI() {
        val createButton = findViewById<Button>(R.id.create_button)
        val showButton = findViewById<Button>(R.id.show_button)
        val hideButton = findViewById<Button>(R.id.hide_button)
        val changeTitleButton = findViewById<Button>(R.id.change_title_button)
        val moveToCornerButton = findViewById<Button>(R.id.move_to_corner_button)
        val removeButton = findViewById<Button>(R.id.remove_button)
        
        createButton.setOnClickListener {
            createFloatingWindow()
        }
        
        showButton.setOnClickListener {
            floatingWindow?.show()
        }
        
        hideButton.setOnClickListener {
            floatingWindow?.hide()
        }
        
        changeTitleButton.setOnClickListener {
            changeWindowTitle()
        }
        
        moveToCornerButton.setOnClickListener {
            moveToCornerAndResize()
        }
        
        removeButton.setOnClickListener {
            floatingWindow?.hide()
            floatingWindow = null
        }
    }
    
    private fun createFloatingWindow() {
        floatingWindow = FloatingWindow(this)
            .setTitle("演示浮动窗口")
            .setSize(300, 300)  // 修改窗口大小为300*300
            .setPosition(200, 200)
            .setOnCloseListener {
                Toast.makeText(this, "窗口已关闭", Toast.LENGTH_SHORT).show()
            }
            
        // 显示窗口
        val showResult = floatingWindow?.show()
        
        if (showResult == true) {
            // 显示窗口句柄信息
            val viewHandle = floatingWindow?.getViewHandle()
            val layoutParamsHandle = floatingWindow?.getLayoutParamsHandle()
            Log.d("DemoActivity", "创建的窗口视图句柄: " + viewHandle?.toString())
            Log.d("DemoActivity", "创建的窗口参数句柄: " + layoutParamsHandle?.toString())
            Toast.makeText(this, "浮动窗口已创建", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "浮动窗口创建失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun changeWindowTitle() {
        if (floatingWindow != null) {
            floatingWindow?.setTitle("测试标题")
            Toast.makeText(this, "窗口标题已修改为：测试标题", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun moveToCornerAndResize() {
        if (floatingWindow != null) {
            // 设置窗口位置到屏幕左上角(0,0)
            floatingWindow?.setPosition(0, 0)
            // 设置窗口大小为100*100
            floatingWindow?.setSize(100, 100)
            Toast.makeText(this, "窗口已移动到左上角并调整大小为100*100", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
        }
    }
}