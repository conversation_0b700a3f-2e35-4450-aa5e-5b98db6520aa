@echo off
echo 构建LogFloat插件APK...
echo.

echo 构建Debug版本...
gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo Debug版本构建失败!
    exit /b %errorlevel%
)

echo 构建Release版本...
gradlew.bat assembleRelease
if %errorlevel% neq 0 (
    echo Release版本构建失败!
    exit /b %errorlevel%
)

echo.
echo 构建完成!
echo.
echo 生成的APK文件:
echo   - app\build\outputs\apk\debug\app-debug.apk
echo   - app\build\outputs\apk\release\app-release-unsigned.apk
echo.
echo 要安装到设备上进行测试，请使用adb命令:
echo   adb install app\build\outputs\apk\debug\app-debug.apk