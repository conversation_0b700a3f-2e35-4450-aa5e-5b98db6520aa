//.vscode/settings.json
{
  // 让 VS Code 的文件搜索、智能提示、Kilo Code 等都忽略这些目录
  "files.exclude": {
    "**/dist": true,
    "**/build": true,
    "**/node_modules": true,
    "**/*.generated.*": true,
    "**/*.log": true  // 排除所有 .log 文件
  },
  // 如果你还装了其他 AI 插件，再补一条
  "search.exclude": {
    "**/dist": true,
    "**/build": true,
    "**/node_modules": true,
    "**/*.log": true  // 排除所有 .log 文件
  },
  "kiloCode.exclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/build": true,
    "**/*.log": true  // 排除所有 .log 文件
  }

}