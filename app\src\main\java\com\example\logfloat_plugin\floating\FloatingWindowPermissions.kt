package com.example.logfloat_plugin.floating

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log

/**
 * 处理浮动窗口所需权限的辅助类
 */
object FloatingWindowPermissions {
    
    private const val TAG = "浮动窗口权限"
    
    /**
     * 检查应用是否具有在其他应用上层绘制的权限
     */
    fun hasOverlayPermission(context: Context): Boolean {
        Log.d(TAG, "检查悬浮窗权限")
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val hasPermission = Settings.canDrawOverlays(context)
            Log.d(TAG, "检查悬浮窗权限结果: " + hasPermission)
            hasPermission
        } else {
            Log.d(TAG, "检查悬浮窗权限: 旧版本Android无需此权限，返回true")
            true // Not required for older versions
        }
    }
    
    /**
     * 请求在其他应用上层绘制的权限
     */
    fun requestOverlayPermission(context: Context) {
        Log.d(TAG, "请求悬浮窗权限")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:" + context.packageName)
            )
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.d(TAG, "请求悬浮窗权限: 已启动权限请求界面")
        } else {
            Log.d(TAG, "请求悬浮窗权限: 旧版本Android无需此权限")
        }
    }
}