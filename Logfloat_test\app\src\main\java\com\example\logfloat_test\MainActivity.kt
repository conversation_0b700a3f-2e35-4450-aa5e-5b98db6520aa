package com.example.logfloat_test

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

class MainActivity : AppCompatActivity() {
    
    private var floatingWindowInstance: Any? = null
    private var pluginLoader: PluginLoader? = null
    private var apkPath: String? = null
    private lateinit var permissionStatusText: TextView
    
    // Register the launcher for requesting overlay permission
    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        updatePermissionStatus()
    }
    
    // Register the launcher for requesting storage permission
    private val storagePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        updatePermissionStatus()
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        permissionStatusText = findViewById(R.id.permission_status)
        
        // Copy plugin APK from assets to app files directory
        copyPluginFromAssets()
        
        // Request necessary permissions
        requestPermissions()
        
        // Initialize buttons
        initButtons()
        
        // Initial permission status update
        updatePermissionStatus()
    }
    
    private fun initButtons() {
        val loadPluginButton = findViewById<Button>(R.id.load_plugin_button)
        loadPluginButton.setOnClickListener {
            loadPlugin()
        }
        
        val createWindowButton = findViewById<Button>(R.id.create_window_button)
        createWindowButton.setOnClickListener {
            createFloatingWindow()
        }
        
        val showWindowButton = findViewById<Button>(R.id.show_window_button)
        showWindowButton.setOnClickListener {
            showFloatingWindow()
        }
        
        val hideWindowButton = findViewById<Button>(R.id.hide_window_button)
        hideWindowButton.setOnClickListener {
            hideFloatingWindow()
        }
        
        val changeTitleButton = findViewById<Button>(R.id.change_title_button)
        changeTitleButton.setOnClickListener {
            changeWindowTitle()
        }
        
        val moveToCornerButton = findViewById<Button>(R.id.move_to_corner_button)
        moveToCornerButton.setOnClickListener {
            moveToCornerAndResize()
        }
        
        val removeWindowButton = findViewById<Button>(R.id.remove_window_button)
        removeWindowButton.setOnClickListener {
            removeFloatingWindow()
        }
        
        val checkPermissionsButton = findViewById<Button>(R.id.check_permissions_button)
        checkPermissionsButton.setOnClickListener {
            updatePermissionStatus()
        }
    }
    
    private fun updatePermissionStatus() {
        val hasStoragePermission = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
        val hasOverlayPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
        
        val statusText = "权限检测结果：" + "存储权限: " + (if (hasStoragePermission) "已授予" else "未授予") + "" + "悬浮窗权限: " + (if (hasOverlayPermission) "已授予" else "未授予")
        
        permissionStatusText.text = statusText
    }
    
    private fun copyPluginFromAssets() {
        try {
            // Create plugin directory if it doesn't exist
            val pluginDir = File(filesDir, "plugin")
            if (!pluginDir.exists()) {
                pluginDir.mkdirs()
            }
            
            // Copy the APK file from assets to plugin directory
            val assetManager = assets
            val inputStream: InputStream = assetManager.open("plugin/app-debug.apk")
            val outputFile = File(pluginDir, "app-debug.apk")
            val outputStream = FileOutputStream(outputFile)
            
            inputStream.copyTo(outputStream)
            
            inputStream.close()
            outputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun requestPermissions() {
        // Request storage permissions to access APK file
        storagePermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
        
        // Request SYSTEM_ALERT_WINDOW permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:$packageName")
                )
                overlayPermissionLauncher.launch(intent)
            }
        }
    }
    
    private fun loadPlugin() {
        try {
            // Check if required permissions are granted
            if (!PluginLoader.checkRequiredPermissions(this)) {
                val msg = "请在应用设置中授予所有必要权限"
                Toast.makeText(this, msg, Toast.LENGTH_LONG).show()
                updatePermissionStatus()
                return
            }
            
            // APK file path - The plugin APK file is stored in the app's files directory
            // Build the logfloat_plugin project to generate the APK, then place it in the assets/plugin folder
            apkPath = File(filesDir, "plugin/app-debug.apk").absolutePath
            val apkFile = File(apkPath!!)
            
            if (!apkFile.exists()) {
                val msg = "插件APK文件未找到: $apkPath。请确保APK位于assets/plugin/目录下"
                Toast.makeText(this, msg, Toast.LENGTH_LONG).show()
                return
            }
            
            // Create plugin loader
            pluginLoader = PluginLoader(this)
            
            // Try to load the FloatingWindow class from the plugin
            val floatingWindowClass = pluginLoader!!.loadClassFromApk(
                apkPath!!, 
                "com.example.logfloat_plugin.floating.FloatingWindow"
            )
            
            if (floatingWindowClass != null) {
                val msg = "插件加载成功! 加载的类: ${floatingWindowClass.name}"
                Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
                
                // Try to load the FloatingWindowManager class
                val windowManagerClass = pluginLoader!!.loadClassFromApk(
                    apkPath!!,
                    "com.example.logfloat_plugin.floating.FloatingWindowManager"
                )
                
                if (windowManagerClass != null) {
                    Toast.makeText(this, "FloatingWindowManager加载成功!", Toast.LENGTH_SHORT).show()
                } else {
                    val errorMsg = "加载FloatingWindowManager类失败"
                    Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
                }
            } else {
                val errorMsg = "加载插件类失败"
                Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            }
            
        } catch (e: Exception) {
            val errorMsg = "加载插件时出错: ${e.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }
    
    private fun createFloatingWindow() {
        if (pluginLoader == null || apkPath == null) {
            Toast.makeText(this, "请先加载插件", Toast.LENGTH_SHORT).show()
            return
        }
        
        // Check if required permissions are granted
        if (!PluginLoader.checkRequiredPermissions(this)) {
            val msg = "请授予悬浮窗权限以创建浮动窗口"
            Toast.makeText(this, msg, Toast.LENGTH_LONG).show()
            updatePermissionStatus()
            return
        }
        
        try {
            // Create an instance of FloatingWindow
            floatingWindowInstance = pluginLoader!!.createInstance(
                apkPath!!,
                "com.example.logfloat_plugin.floating.FloatingWindow",
                arrayOf(this),  // Constructor parameters
                arrayOf(android.content.Context::class.java)  // Constructor parameter types
            )
            
            if (floatingWindowInstance != null) {
                // Set initial window properties using reflection
                pluginLoader!!.invokeMethodUnit(
                    floatingWindowInstance!!,
                    "setTitle",
                    arrayOf("测试窗口"),
                    arrayOf(java.lang.String::class.java)
                )
                
                pluginLoader!!.invokeMethodUnit(
                    floatingWindowInstance!!,
                    "setSize",
                    arrayOf(300, 300),
                    arrayOf(java.lang.Integer.TYPE, java.lang.Integer.TYPE)
                )
                
                pluginLoader!!.invokeMethodUnit(
                    floatingWindowInstance!!,
                    "setPosition",
                    arrayOf(200, 200),
                    arrayOf(java.lang.Integer.TYPE, java.lang.Integer.TYPE)
                )
                
                // Show the floating window immediately after creation
                val showResult = pluginLoader!!.invokeMethod(floatingWindowInstance!!, "show")
                
                // 获取并显示窗口句柄信息
                val viewHandle = pluginLoader!!.invokeMethod(floatingWindowInstance!!, "getViewHandle")
                val layoutParamsHandle = pluginLoader!!.invokeMethod(floatingWindowInstance!!, "getLayoutParamsHandle")
                Log.d("Logfloat_test", "创建的窗口视图句柄: " + viewHandle?.toString())
                Log.d("Logfloat_test", "创建的窗口参数句柄: " + layoutParamsHandle?.toString())
                
                if (showResult == true) {
                    val msg = "浮动窗口创建并显示成功! 窗口句柄信息已记录到日志。"
                    Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
                } else {
                    val errorMsg = "显示浮动窗口失败"
                    Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
                }
            } else {
                val errorMsg = "创建FloatingWindow实例失败"
                Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            val errorMsg = "创建浮动窗口时出错: ${e.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }
    
    private fun showFloatingWindow() {
        if (floatingWindowInstance == null || pluginLoader == null) {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
            return
        }
        
        // Check if required permissions are granted
        if (!PluginLoader.checkRequiredPermissions(this)) {
            val msg = "请授予悬浮窗权限以显示浮动窗口"
            Toast.makeText(this, msg, Toast.LENGTH_LONG).show()
            updatePermissionStatus()
            return
        }
        
        // Show the floating window
        val showException = pluginLoader!!.invokeMethodUnit(floatingWindowInstance!!, "show", arrayOf(), arrayOf())
        if (showException == null) {
            val msg = "浮动窗口已显示!"
            Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
        } else {
            val errorMsg = "显示浮动窗口时出错: ${showException.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            showException.printStackTrace()
        }
    }
    
    private fun hideFloatingWindow() {
        if (floatingWindowInstance == null || pluginLoader == null) {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
            return
        }
        
        // Hide the floating window
        val hideException = pluginLoader!!.invokeMethodUnit(floatingWindowInstance!!, "hide", arrayOf(), arrayOf())
        if (hideException == null) {
            val msg = "浮动窗口已隐藏!"
            Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
        } else {
            val errorMsg = "隐藏浮动窗口时出错: ${hideException.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            hideException.printStackTrace()
        }
    }
    
    private fun changeWindowTitle() {
        if (floatingWindowInstance == null || pluginLoader == null) {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
            return
        }
        
        val setTitleException = pluginLoader!!.invokeMethodUnit(
            floatingWindowInstance!!,
            "setTitle",
            arrayOf("测试标题"),
            arrayOf(java.lang.String::class.java)
        )
        if (setTitleException == null) {
            val msg = "窗口标题修改成功"
            Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
        } else {
            val errorMsg = "修改窗口标题时出错: ${setTitleException.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            setTitleException.printStackTrace()
        }
    }
    
    private fun moveToCornerAndResize() {
        if (floatingWindowInstance == null || pluginLoader == null) {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
            return
        }
        
        // Set window position to top-left corner (0,0)
        val setPositionException = pluginLoader!!.invokeMethodUnit(
            floatingWindowInstance!!,
            "setPosition",
            arrayOf(0, 0),
            arrayOf(java.lang.Integer.TYPE, java.lang.Integer.TYPE)
        )
        
        if (setPositionException != null) {
            val errorMsg = "设置窗口位置时出错: ${setPositionException.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            setPositionException.printStackTrace()
            return
        }
        
        // Set window size to 100*100
        val setSizeException = pluginLoader!!.invokeMethodUnit(
            floatingWindowInstance!!,
            "setSize",
            arrayOf(100, 100),
            arrayOf(java.lang.Integer.TYPE, java.lang.Integer.TYPE)
        )
        
        if (setSizeException != null) {
            val errorMsg = "设置窗口大小时出错: ${setSizeException.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            setSizeException.printStackTrace()
            return
        }
        
        val msg = "窗口已移动到左上角并调整大小为100*100"
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
    }
    
    private fun removeFloatingWindow() {
        if (floatingWindowInstance == null || pluginLoader == null) {
            Toast.makeText(this, "请先创建浮动窗口", Toast.LENGTH_SHORT).show()
            return
        }
        
        // Hide and remove the floating window
        val hideException = pluginLoader!!.invokeMethodUnit(floatingWindowInstance!!, "hide", arrayOf(), arrayOf())
        if (hideException != null) {
            val errorMsg = "隐藏浮动窗口时出错: ${hideException.message}"
            Toast.makeText(this, errorMsg, Toast.LENGTH_LONG).show()
            hideException.printStackTrace()
            return
        }
        
        floatingWindowInstance = null
        val msg = "浮动窗口已移除!"
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
    }
}