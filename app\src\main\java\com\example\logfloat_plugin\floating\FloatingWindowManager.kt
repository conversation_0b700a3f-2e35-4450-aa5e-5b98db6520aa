package com.example.logfloat_plugin.floating

import android.content.Context
import android.util.Log

/**
 * 管理多个浮动窗口
 */
object FloatingWindowManager {
    
    private val windows = mutableMapOf<String, FloatingWindow>()
    
    private const val TAG = "浮动窗口管理器"
    
    /**
     * 创建一个新的浮动窗口并指定ID
     */
    fun createWindow(context: Context, id: String): FloatingWindow {
        Log.d(TAG, "创建窗口: ID=" + id)
        val window = FloatingWindow(context)
        windows[id] = window
        Log.d(TAG, "窗口创建并存储，ID=" + id + "，总窗口数=" + windows.size)
        return window
    }
    
    /**
     * 通过ID获取现有的浮动窗口
     */
    fun getWindow(id: String): FloatingWindow? {
        Log.d(TAG, "获取窗口: ID=" + id)
        val window = windows[id]
        Log.d(TAG, "获取窗口结果: " + if (window != null) "找到" else "未找到")
        return window
    }
    
    /**
     * 通过ID显示窗口
     */
    fun showWindow(id: String) {
        Log.d(TAG, "显示窗口: ID=" + id)
        val window = windows[id]
        if (window != null) {
            window.show()
            Log.d(TAG, "窗口已显示: ID=" + id)
        } else {
            Log.w(TAG, "显示窗口: 未找到ID为" + id + "的窗口")
        }
    }
    
    /**
     * 通过ID隐藏窗口
     */
    fun hideWindow(id: String) {
        Log.d(TAG, "隐藏窗口: ID=" + id)
        val window = windows[id]
        if (window != null) {
            window.hide()
            Log.d(TAG, "窗口已隐藏: ID=" + id)
        } else {
            Log.w(TAG, "隐藏窗口: 未找到ID为" + id + "的窗口")
        }
    }
    
    /**
     * 通过ID检查窗口是否正在显示
     */
    fun isWindowShowing(id: String): Boolean {
        Log.d(TAG, "检查窗口是否正在显示: ID=" + id)
        val window = windows[id]
        val isShowing = window?.isShowing() ?: false
        Log.d(TAG, "检查窗口是否正在显示结果: " + isShowing)
        return isShowing
    }
    
    /**
     * 通过ID移除窗口
     */
    fun removeWindow(id: String) {
        Log.d(TAG, "移除窗口: ID=" + id)
        val window = windows[id]
        if (window != null) {
            window.hide()
            windows.remove(id)
            Log.d(TAG, "窗口已移除: ID=" + id + "，总窗口数=" + windows.size)
        } else {
            Log.w(TAG, "移除窗口: 未找到ID为" + id + "的窗口")
        }
    }
    
    /**
     * 隐藏所有窗口
     */
    fun hideAllWindows() {
        Log.d(TAG, "隐藏所有窗口: 正在隐藏" + windows.size + "个窗口")
        windows.values.forEach { it.hide() }
        Log.d(TAG, "隐藏所有窗口: 完成")
    }
    
    /**
     * 移除所有窗口
     */
    fun removeAllWindows() {
        Log.d(TAG, "移除所有窗口: 正在移除" + windows.size + "个窗口")
        hideAllWindows()
        windows.clear()
        Log.d(TAG, "移除所有窗口: 完成，总窗口数=" + windows.size)
    }
}