package com.example.logfloat_test

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.Button
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import dalvik.system.DexClassLoader
import java.io.File
import java.lang.reflect.Method

/**
 * Utility class for loading classes from an APK file using DEX class loading
 */
class PluginLoader(private val context: Context) {
    
    /**
     * Load a class from an APK file
     * 
     * @param apkPath Path to the APK file
     * @param className Full name of the class to load
     * @return The loaded Class object, or null if loading failed
     */
    fun loadClassFromApk(apkPath: String, className: String): Class<*>? {
        try {
            // Create a directory for extracted DEX files
            val dexDir = File(context.cacheDir, "dex").apply { mkdirs() }
            
            // Create the DEX class loader
            val dexClassLoader = DexClassLoader(
                apkPath,  // APK path
                dexDir.absolutePath,  // Directory for optimized DEX files
                null,  // Library search path (null for now)
                context.classLoader  // Parent class loader
            )
            
            // Load the requested class
            return dexClassLoader.loadClass(className)
            
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Create an instance of a class from the plugin APK
     * 
     * @param apkPath Path to the APK file
     * @param className Full name of the class to instantiate
     * @param constructorParams Parameters for the constructor
     * @param constructorTypes Types of the constructor parameters
     * @return The created instance, or null if instantiation failed
     */
    fun createInstance(
        apkPath: String,
        className: String,
        constructorParams: Array<Any?>,
        constructorTypes: Array<Class<*>>
    ): Any? {
        try {
            // Load the class
            val clazz = loadClassFromApk(apkPath, className)
            if (clazz == null) {
                println("Failed to load class: $className")
                return null
            }
            
            // Get the constructor
            val constructor = clazz.getConstructor(*constructorTypes)
            
            // Create an instance
            return constructor.newInstance(*constructorParams)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Invoke a method on an object loaded from the plugin APK
     * 
     * @param instance The object instance
     * @param methodName Name of the method to invoke
     * @param methodParams Parameters for the method
     * @param methodTypes Types of the method parameters
     * @return The result of the method call, or null if invocation failed
     */
    fun invokeMethod(
        instance: Any,
        methodName: String,
        methodParams: Array<Any?>,
        methodTypes: Array<Class<*>>
    ): Any? {
        try {
            // Get the method
            val method = instance.javaClass.getMethod(methodName, *methodTypes)
            
            // Invoke the method
            return method.invoke(instance, *methodParams)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * Invoke a method on an object loaded from the plugin APK that returns Unit (void in Java)
     * 
     * @param instance The object instance
     * @param methodName Name of the method to invoke
     * @param methodParams Parameters for the method
     * @param methodTypes Types of the method parameters
     * @return Exception if occurred, null otherwise
     */
    fun invokeMethodUnit(
        instance: Any,
        methodName: String,
        methodParams: Array<Any?>,
        methodTypes: Array<Class<*>>
    ): Exception? {
        try {
            // Get the method
            val method = instance.javaClass.getMethod(methodName, *methodTypes)
            
            // Invoke the method
            method.invoke(instance, *methodParams)
            return null
        } catch (e: Exception) {
            e.printStackTrace()
            return e
        }
    }
    
    /**
     * Invoke a method on an object loaded from the plugin APK (convenience method for methods with no parameters)
     * 
     * @param instance The object instance
     * @param methodName Name of the method to invoke
     * @return The result of the method call, or null if invocation failed
     */
    fun invokeMethod(instance: Any, methodName: String): Any? {
        return invokeMethod(instance, methodName, emptyArray(), emptyArray())
    }
    
    /**
     * Set a field value on an object loaded from the plugin APK
     * 
     * @param instance The object instance
     * @param fieldName Name of the field to set
     * @param value The value to set
     */
    fun setField(instance: Any, fieldName: String, value: Any?) {
        try {
            val field = instance.javaClass.getDeclaredField(fieldName)
            field.isAccessible = true
            field.set(instance, value)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    companion object {
        /**
         * Check if the required permissions are granted for the plugin to work
         *
         * @param context The application context
         * @return true if all required permissions are granted, false otherwise
         */
        fun checkRequiredPermissions(context: Context): Boolean {
            // Check SYSTEM_ALERT_WINDOW permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.canDrawOverlays(context)) {
                    return false
                }
            }
            
            return true
        }
    }
}