<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="浮动窗口演示"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <Button
        android:id="@+id/create_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="创建浮动窗口"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/show_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="显示浮动窗口"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/hide_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="隐藏浮动窗口"
        android:layout_marginBottom="16dp" />
        
    <Button
        android:id="@+id/change_title_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="修改标题为：测试标题"
        android:layout_marginBottom="16dp" />
        
    <Button
        android:id="@+id/move_to_corner_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="移动到左上角并设置为100*100"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/remove_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="移除浮动窗口" />

</LinearLayout>