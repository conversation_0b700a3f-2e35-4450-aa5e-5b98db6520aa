<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#AAFF0000">

    <!-- Title Bar -->
    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="#CC333333"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Floating Window"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp" />

        <Button
            android:id="@+id/close_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="X"
            android:textColor="#FFFFFF"
            android:background="#00000000" />

    </LinearLayout>

    <!-- Content Area -->
    <FrameLayout
        android:id="@+id/content_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="10dp"
        android:background="#88000000">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Floating Window Content"
            android:textColor="#FFFFFF"
            android:layout_gravity="center"
            android:textSize="18sp"
            android:textStyle="bold" />

    </FrameLayout>

</LinearLayout>