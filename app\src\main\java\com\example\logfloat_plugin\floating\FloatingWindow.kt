package com.example.logfloat_plugin.floating

import android.content.Context
import android.graphics.PixelFormat
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import android.view.MotionEvent
import com.example.logfloat_plugin.R

/**
 * A floating window that can be displayed over other apps.
 */
class FloatingWindow(private val context: Context) {
    
    private var windowManager: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var floatingView: View? = null
    private var params: WindowManager.LayoutParams? = null
    private var isShowing = false
    
    private var title = "Floating Window"
    private var width = 300  // 修改默认宽度为300
    private var height = 300 // 修改默认高度为300
    private var x = 100
    private var y = 100
    private var showTitleBar = true
    private var onCloseListener: (() -> Unit)? = null
    
    companion object {
        private const val TAG = "浮动窗口"
    }
    
    /**
     * 设置浮动窗口的标题
     */
    fun setTitle(title: String): FloatingWindow {
        Log.d(TAG, "设置标题: " + title)
        this.title = title
        updateTitle()
        return this
    }
    
    /**
     * 设置浮动窗口的大小
     */
    fun setSize(width: Int, height: Int): FloatingWindow {
        Log.d(TAG, "设置大小: 宽度=" + width + ", 高度=" + height)
        this.width = width
        this.height = height
        updateLayout()
        return this
    }
    
    /**
     * 设置浮动窗口的位置
     */
    fun setPosition(x: Int, y: Int): FloatingWindow {
        Log.d(TAG, "设置位置: x=" + x + ", y=" + y)
        this.x = x
        this.y = y
        updateLayout()
        return this
    }
    
    /**
     * 显示或隐藏标题栏
     */
    fun setShowTitleBar(show: Boolean): FloatingWindow {
        Log.d(TAG, "设置显示标题栏: " + show)
        this.showTitleBar = show
        // This would require updating the view layout
        return this
    }
    
    /**
     * 设置窗口关闭时的监听器
     */
    fun setOnCloseListener(listener: () -> Unit): FloatingWindow {
        Log.d(TAG, "设置关闭监听器")
        this.onCloseListener = listener
        return this
    }
    
    /**
     * 显示浮动窗口
     * @return true if the window was successfully shown, false otherwise
     */
    fun show(): Boolean {
        Log.d(TAG, "显示窗口: 是否正在显示=" + isShowing)
        if (isShowing) return true
        
        // 创建浮动视图
        val inflater = LayoutInflater.from(context)
        floatingView = inflater.inflate(R.layout.floating_window_layout, null)
        Log.d(TAG, "加载浮动窗口布局")
        
        // 设置窗口参数（使用当前的width, height, x, y值）
        params = WindowManager.LayoutParams(
            <EMAIL>,
            <EMAIL>,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or 
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or 
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        ).apply {
            this.x = this@FloatingWindow.x
            this.y = this@FloatingWindow.y
            gravity = Gravity.TOP or Gravity.LEFT
        }
        Log.d(TAG, "创建窗口参数: 宽度=" + this.width + ", 高度=" + this.height + ", x=" + this.x + ", y=" + this.y)
        
        // 设置标题
        val titleView = floatingView?.findViewById<TextView>(R.id.title_text)
        titleView?.text = title
        Log.d(TAG, "设置窗口标题: " + title)
        
        // 设置关闭按钮
        val closeButton = floatingView?.findViewById<Button>(R.id.close_button)
        closeButton?.setOnClickListener {
            Log.d(TAG, "关闭按钮被点击")
            hide()
            onCloseListener?.invoke()
        }
        Log.d(TAG, "设置关闭按钮监听器")
        
        // 使窗口可拖拽
        setupDraggable()
        Log.d(TAG, "设置可拖拽功能")
        
        // 将视图添加到窗口管理器
        try {
            windowManager.addView(floatingView, params)
            isShowing = true
            Log.d(TAG, "将视图添加到窗口管理器，窗口现在正在显示")
            
            Log.d(TAG, "窗口句柄 (View对象): " + floatingView?.toString())
            Log.d(TAG, "窗口参数句柄 (LayoutParams对象): " + params?.toString())
            return true
        } catch (e: Exception) {
            Log.e(TAG, "添加视图到窗口管理器时出错: " + e.message)
            e.printStackTrace()
            Log.d(TAG, "窗口句柄 (View对象): " + floatingView?.toString())
            Log.d(TAG, "窗口参数句柄 (LayoutParams对象): " + params?.toString())
            return false
        }
    }
    
    /**
     * 隐藏浮动窗口
     */
    fun hide() {
        Log.d(TAG, "隐藏窗口: 是否正在显示=" + isShowing + ", 浮动视图是否为空=" + (floatingView == null))
        Log.d(TAG, "窗口句柄 (View对象): " + floatingView?.toString())
        Log.d(TAG, "窗口参数句柄 (LayoutParams对象): " + params?.toString())
        if (!isShowing || floatingView == null) return
        
        try {
            windowManager.removeView(floatingView)
            isShowing = false
            Log.d(TAG, "从窗口管理器移除视图，窗口现在已隐藏")
        } catch (e: Exception) {
            Log.e(TAG, "从窗口管理器移除视图时出错: " + e.message)
            e.printStackTrace()
        }
    }
    
    /**
     * 检查浮动窗口当前是否正在显示
     */
    fun isShowing(): Boolean {
        Log.d(TAG, "检查是否正在显示: " + isShowing)
        return isShowing
    }
    
    /**
     * 获取窗口视图句柄
     */
    fun getViewHandle(): android.view.View? {
        Log.d(TAG, "获取窗口视图句柄: " + floatingView?.toString())
        return floatingView
    }
    
    /**
     * 获取窗口参数句柄
     */
    fun getLayoutParamsHandle(): android.view.WindowManager.LayoutParams? {
        Log.d(TAG, "获取窗口参数句柄: " + params?.toString())
        return params
    }
    
    /**
     * 如果窗口正在显示，则更新标题
     */
    private fun updateTitle() {
        if (!isShowing) return
        val titleView = floatingView?.findViewById<TextView>(R.id.title_text)
        titleView?.text = title
        Log.d(TAG, "更新标题: " + title)
    }
    
    /**
     * 更新布局参数
     */
    private fun updateLayout() {
        // 如果窗口未显示，参数会在显示时使用最新的值
        if (!isShowing || params == null || floatingView == null) {
            Log.d(TAG, "窗口未显示，参数将在显示时更新")
            return
        }
        
        // 如果窗口已显示，则更新布局参数
        params?.width = width
        params?.height = height
        params?.x = x
        params?.y = y
        
        try {
            windowManager.updateViewLayout(floatingView, params)
            Log.d(TAG, "更新布局: 宽度=" + width + ", 高度=" + height + ", x=" + x + ", y=" + y)
        } catch (e: Exception) {
            Log.e(TAG, "更新布局时出错: " + e.message)
        }
    }
    
    /**
     * 使浮动窗口可拖拽
     */
    private fun setupDraggable() {
        val titleBar = floatingView?.findViewById<View>(R.id.title_bar)
        titleBar?.setOnTouchListener(object : View.OnTouchListener {
            private var initialX = 0
            private var initialY = 0
            private var initialTouchX = 0f
            private var initialTouchY = 0f
            
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params?.x ?: 0
                        initialY = params?.y ?: 0
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        Log.d(TAG, "开始拖拽: initialX=" + initialX + ", initialY=" + initialY)
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        Log.d(TAG, "结束拖拽")
                        // Handle click events if needed
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params?.x = initialX + (event.rawX - initialTouchX).toInt()
                        params?.y = initialY + (event.rawY - initialTouchY).toInt()
                        try {
                            windowManager.updateViewLayout(floatingView, params)
                        } catch (e: Exception) {
                            Log.e(TAG, "拖拽时更新布局出错: " + e.message)
                        }
                        // Not logging every move event as it would be too verbose
                        return true
                    }
                }
                return false
            }
        })
    }
}