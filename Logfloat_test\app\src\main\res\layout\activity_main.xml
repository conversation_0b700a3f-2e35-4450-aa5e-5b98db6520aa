<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="LogFloat插件测试器"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <Button
        android:id="@+id/load_plugin_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="1. 加载插件"
        android:layout_marginBottom="8dp" />
        
    <Button
        android:id="@+id/create_window_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="2. 创建浮动窗口"
        android:layout_marginBottom="8dp" />
        
    <Button
        android:id="@+id/show_window_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="3. 显示浮动窗口"
        android:layout_marginBottom="8dp" />
        
    <Button
        android:id="@+id/hide_window_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="4. 隐藏浮动窗口"
        android:layout_marginBottom="8dp" />
        
    <Button
        android:id="@+id/change_title_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="5. 修改窗口标题为：测试标题"
        android:layout_marginBottom="8dp" />
        
    <Button
        android:id="@+id/move_to_corner_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="6. 移动到左上角并设置为100*100"
        android:layout_marginBottom="8dp" />
        
    <Button
        android:id="@+id/remove_window_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="7. 移除浮动窗口"
        android:layout_marginBottom="16dp" />
        
    <TextView
        android:id="@+id/permission_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="权限检测结果："
        android:textSize="16sp"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/check_permissions_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="检测权限" />

</LinearLayout>